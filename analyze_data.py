#!/usr/bin/env python3
"""
Data Analysis Script for Indonesian Stiker Dinding Reviews
Analyzes the structure and content of the review dataset
"""

import pandas as pd
import glob
import os
from collections import Counter
import re
import numpy as np

def analyze_data_structure():
    """Analyze the structure and content of all CSV files"""
    
    print("=== INDONESIAN STIKER DINDING DATASET ANALYSIS ===\n")
    
    # Get all CSV files
    csv_files = glob.glob("*.csv")
    print(f"Total CSV files found: {len(csv_files)}")
    
    # Initialize counters
    total_records = 0
    rating_distribution = Counter()
    review_lengths = []
    empty_reviews = 0
    files_with_data = 0
    
    # Sample some files for detailed analysis
    sample_files = csv_files[:10]  # First 10 files
    
    print("\n=== SAMPLE FILE ANALYSIS ===")
    for i, file in enumerate(sample_files):
        try:
            df = pd.read_csv(file, encoding='utf-8')
            if len(df) > 0:
                files_with_data += 1
                print(f"\nFile {i+1}: {file}")
                print(f"  Records: {len(df)}")
                print(f"  Columns: {list(df.columns)}")
                
                # Show sample reviews
                if 'review' in df.columns and len(df) > 0:
                    sample_review = df['review'].iloc[0] if pd.notna(df['review'].iloc[0]) else "Empty"
                    print(f"  Sample review: {sample_review[:100]}...")
                    
                if 'rating' in df.columns:
                    ratings = df['rating'].value_counts().sort_index()
                    print(f"  Rating distribution: {dict(ratings)}")
                    
        except Exception as e:
            print(f"Error reading {file}: {e}")
    
    print(f"\nFiles with data: {files_with_data}")
    
    # Analyze all files for comprehensive statistics
    print("\n=== COMPREHENSIVE ANALYSIS ===")
    
    for file in csv_files:
        try:
            df = pd.read_csv(file, encoding='utf-8')
            if len(df) > 0 and 'review' in df.columns and 'rating' in df.columns:
                total_records += len(df)
                
                # Count ratings
                for rating in df['rating']:
                    if pd.notna(rating):
                        rating_distribution[rating] += 1
                
                # Analyze review lengths
                for review in df['review']:
                    if pd.notna(review) and str(review).strip():
                        review_lengths.append(len(str(review)))
                    else:
                        empty_reviews += 1
                        
        except Exception as e:
            print(f"Error processing {file}: {e}")
    
    # Print comprehensive statistics
    print(f"\nTotal records across all files: {total_records}")
    print(f"Empty/null reviews: {empty_reviews}")
    print(f"Valid reviews: {len(review_lengths)}")
    
    print(f"\n=== RATING DISTRIBUTION ===")
    for rating in sorted(rating_distribution.keys()):
        count = rating_distribution[rating]
        percentage = (count / total_records) * 100 if total_records > 0 else 0
        print(f"Rating {rating}: {count} ({percentage:.1f}%)")
    
    print(f"\n=== REVIEW LENGTH STATISTICS ===")
    if review_lengths:
        print(f"Average review length: {np.mean(review_lengths):.1f} characters")
        print(f"Median review length: {np.median(review_lengths):.1f} characters")
        print(f"Min review length: {min(review_lengths)} characters")
        print(f"Max review length: {max(review_lengths)} characters")
        print(f"Standard deviation: {np.std(review_lengths):.1f} characters")
    
    # Analyze content patterns
    print(f"\n=== CONTENT PATTERN ANALYSIS ===")
    
    # Sample reviews for pattern analysis
    sample_reviews = []
    for file in csv_files[:20]:  # Sample from first 20 files
        try:
            df = pd.read_csv(file, encoding='utf-8')
            if len(df) > 0 and 'review' in df.columns:
                for review in df['review'].head(5):  # Take first 5 from each file
                    if pd.notna(review) and str(review).strip():
                        sample_reviews.append(str(review))
        except:
            continue
    
    # Analyze patterns in sample reviews
    short_reviews = [r for r in sample_reviews if len(r) <= 20]
    medium_reviews = [r for r in sample_reviews if 20 < len(r) <= 100]
    long_reviews = [r for r in sample_reviews if len(r) > 100]
    
    print(f"Short reviews (≤20 chars): {len(short_reviews)}")
    print(f"Medium reviews (21-100 chars): {len(medium_reviews)}")
    print(f"Long reviews (>100 chars): {len(long_reviews)}")
    
    # Show examples
    if short_reviews:
        print(f"\nShort review examples:")
        for i, review in enumerate(short_reviews[:3]):
            print(f"  {i+1}. '{review}'")
    
    if medium_reviews:
        print(f"\nMedium review examples:")
        for i, review in enumerate(medium_reviews[:3]):
            print(f"  {i+1}. '{review[:80]}...'")
    
    if long_reviews:
        print(f"\nLong review examples:")
        for i, review in enumerate(long_reviews[:2]):
            print(f"  {i+1}. '{review[:100]}...'")
    
    return {
        'total_files': len(csv_files),
        'total_records': total_records,
        'rating_distribution': dict(rating_distribution),
        'review_length_stats': {
            'mean': np.mean(review_lengths) if review_lengths else 0,
            'median': np.median(review_lengths) if review_lengths else 0,
            'min': min(review_lengths) if review_lengths else 0,
            'max': max(review_lengths) if review_lengths else 0,
            'std': np.std(review_lengths) if review_lengths else 0
        },
        'empty_reviews': empty_reviews
    }

if __name__ == "__main__":
    stats = analyze_data_structure()
    print(f"\n=== ANALYSIS COMPLETE ===")
