#!/usr/bin/env python3
"""
Data Consolidation Script for Indonesian Stiker Dinding Reviews
Merges all CSV files into a unified dataset with quality checks and deduplication
"""

import pandas as pd
import glob
import os
import hashlib
from datetime import datetime
import numpy as np

def clean_text(text):
    """Clean and normalize text data"""
    if pd.isna(text):
        return ""
    
    text = str(text).strip()
    # Remove excessive whitespace
    text = ' '.join(text.split())
    return text

def calculate_text_hash(text):
    """Calculate hash for duplicate detection"""
    if not text:
        return ""
    return hashlib.md5(text.encode('utf-8')).hexdigest()

def consolidate_dataset():
    """Consolidate all CSV files into a unified dataset"""
    
    print("=== INDONESIAN STIKER DINDING DATASET CONSOLIDATION ===\n")
    
    # Get all CSV files
    csv_files = glob.glob("*.csv")
    print(f"Found {len(csv_files)} CSV files to process")
    
    # Initialize consolidated dataset
    consolidated_data = []
    
    # Statistics tracking
    total_files_processed = 0
    total_records_processed = 0
    total_valid_records = 0
    total_empty_reviews = 0
    total_invalid_ratings = 0
    files_with_errors = []
    
    print("\nProcessing files...")
    
    for i, file in enumerate(csv_files):
        try:
            # Read CSV file
            df = pd.read_csv(file, encoding='utf-8')
            
            if len(df) == 0:
                continue
                
            # Ensure required columns exist
            if 'review' not in df.columns or 'rating' not in df.columns:
                print(f"Warning: {file} missing required columns")
                continue
            
            total_files_processed += 1
            file_records = 0
            
            # Process each record
            for _, row in df.iterrows():
                total_records_processed += 1
                
                # Clean and validate data
                review_text = clean_text(row['review'])
                rating = row['rating']
                
                # Skip empty reviews
                if not review_text:
                    total_empty_reviews += 1
                    continue
                
                # Validate rating
                try:
                    rating = float(rating)
                    if rating not in [1, 2, 3, 4, 5]:
                        total_invalid_ratings += 1
                        continue
                except (ValueError, TypeError):
                    total_invalid_ratings += 1
                    continue
                
                # Add to consolidated dataset
                record = {
                    'review': review_text,
                    'rating': int(rating),
                    'source_file': file,
                    'review_length': len(review_text),
                    'text_hash': calculate_text_hash(review_text.lower())
                }
                
                consolidated_data.append(record)
                file_records += 1
                total_valid_records += 1
            
            # Progress update
            if (i + 1) % 50 == 0:
                print(f"Processed {i + 1}/{len(csv_files)} files...")
                
        except Exception as e:
            files_with_errors.append((file, str(e)))
            print(f"Error processing {file}: {e}")
    
    print(f"\nConsolidation complete!")
    print(f"Files processed: {total_files_processed}")
    print(f"Total records processed: {total_records_processed}")
    print(f"Valid records: {total_valid_records}")
    print(f"Empty reviews skipped: {total_empty_reviews}")
    print(f"Invalid ratings skipped: {total_invalid_ratings}")
    print(f"Files with errors: {len(files_with_errors)}")
    
    if files_with_errors:
        print("\nFiles with errors:")
        for file, error in files_with_errors:
            print(f"  {file}: {error}")
    
    # Create DataFrame
    df_consolidated = pd.DataFrame(consolidated_data)
    
    if len(df_consolidated) == 0:
        print("No valid data found!")
        return None
    
    print(f"\n=== CONSOLIDATED DATASET STATISTICS ===")
    print(f"Total records: {len(df_consolidated)}")
    
    # Rating distribution
    rating_dist = df_consolidated['rating'].value_counts().sort_index()
    print(f"\nRating distribution:")
    for rating, count in rating_dist.items():
        percentage = (count / len(df_consolidated)) * 100
        print(f"  Rating {rating}: {count} ({percentage:.1f}%)")
    
    # Review length statistics
    print(f"\nReview length statistics:")
    print(f"  Mean: {df_consolidated['review_length'].mean():.1f} characters")
    print(f"  Median: {df_consolidated['review_length'].median():.1f} characters")
    print(f"  Min: {df_consolidated['review_length'].min()} characters")
    print(f"  Max: {df_consolidated['review_length'].max()} characters")
    print(f"  Std: {df_consolidated['review_length'].std():.1f} characters")
    
    # Check for duplicates
    print(f"\n=== DUPLICATE ANALYSIS ===")
    duplicate_hashes = df_consolidated['text_hash'].duplicated()
    num_duplicates = duplicate_hashes.sum()
    print(f"Duplicate reviews found: {num_duplicates}")
    
    if num_duplicates > 0:
        print(f"Percentage of duplicates: {(num_duplicates / len(df_consolidated)) * 100:.1f}%")
        
        # Show some duplicate examples
        duplicate_reviews = df_consolidated[duplicate_hashes]['review'].head(5)
        print(f"\nSample duplicate reviews:")
        for i, review in enumerate(duplicate_reviews):
            print(f"  {i+1}. '{review[:80]}...'")
    
    # Remove duplicates (keep first occurrence)
    df_deduplicated = df_consolidated.drop_duplicates(subset=['text_hash'], keep='first')
    print(f"\nAfter deduplication: {len(df_deduplicated)} records")
    
    # Save consolidated dataset
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save with duplicates
    output_file_with_dups = f"consolidated_dataset_with_duplicates_{timestamp}.csv"
    df_consolidated.to_csv(output_file_with_dups, index=False, encoding='utf-8')
    print(f"\nSaved dataset with duplicates: {output_file_with_dups}")
    
    # Save deduplicated
    output_file_clean = f"consolidated_dataset_clean_{timestamp}.csv"
    df_deduplicated.to_csv(output_file_clean, index=False, encoding='utf-8')
    print(f"Saved clean dataset: {output_file_clean}")
    
    # Create summary report
    summary = {
        'consolidation_timestamp': timestamp,
        'total_source_files': len(csv_files),
        'files_processed': total_files_processed,
        'files_with_errors': len(files_with_errors),
        'total_records_processed': total_records_processed,
        'valid_records': total_valid_records,
        'empty_reviews_skipped': total_empty_reviews,
        'invalid_ratings_skipped': total_invalid_ratings,
        'duplicates_found': num_duplicates,
        'final_clean_records': len(df_deduplicated),
        'rating_distribution': dict(rating_dist),
        'review_length_stats': {
            'mean': df_consolidated['review_length'].mean(),
            'median': df_consolidated['review_length'].median(),
            'min': df_consolidated['review_length'].min(),
            'max': df_consolidated['review_length'].max(),
            'std': df_consolidated['review_length'].std()
        }
    }
    
    return df_deduplicated, summary

if __name__ == "__main__":
    result = consolidate_dataset()
    if result:
        df, summary = result
        print(f"\n=== CONSOLIDATION SUCCESSFUL ===")
        print(f"Final dataset contains {len(df)} unique records")
    else:
        print(f"\n=== CONSOLIDATION FAILED ===")
